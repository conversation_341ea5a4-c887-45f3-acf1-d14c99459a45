<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FSU Content Calendar - Restructured</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #e0e0e0;
            overflow-x: auto;
        }
        
        .tab {
            padding: 15px 25px;
            cursor: pointer;
            background: none;
            border: none;
            font-size: 1em;
            color: #555;
            transition: all 0.3s;
            white-space: nowrap;
        }
        
        .tab:hover {
            background: #e8e8e8;
        }
        
        .tab.active {
            background: white;
            color: #1e3c72;
            font-weight: bold;
            border-bottom: 3px solid #1e3c72;
        }
        
        .content {
            padding: 30px;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        .calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 10px;
            margin-top: 20px;
        }
        
        .day-header {
            font-weight: bold;
            padding: 10px;
            background: #f0f0f0;
            text-align: center;
            border-radius: 8px;
        }
        
        .day-cell {
            min-height: 120px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
        }
        
        .content-item {
            background: white;
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 3px solid #667eea;
            font-size: 0.9em;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: transform 0.2s;
        }
        
        .content-item:hover {
            transform: translateX(3px);
        }
        
        .platform-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 12px;
            font-size: 0.75em;
            font-weight: bold;
            margin-right: 5px;
        }
        
        .badge-fb { background: #3b5998; color: white; }
        .badge-ig { background: #e1306c; color: white; }
        .badge-x { background: #000; color: white; }
        .badge-tt { background: #ff0050; color: white; }
        .badge-yt { background: #ff0000; color: white; }
        .badge-li { background: #0077b5; color: white; }
        .badge-email { background: #00a67e; color: white; }
        .badge-podcast { background: #8b5cf6; color: white; }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        
        .status-ready { background: #10b981; }
        .status-draft { background: #f59e0b; }
        .status-scheduled { background: #3b82f6; }
        .status-published { background: #6b7280; }
        
        .pillar-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .pillar-header {
            font-size: 1.3em;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1e3c72;
        }
        
        .pillar-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        
        .pillar-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .workflow-timeline {
            position: relative;
            padding-left: 40px;
            margin: 20px 0;
        }
        
        .workflow-step {
            position: relative;
            padding: 15px;
            background: white;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 3px solid #667eea;
        }
        
        .workflow-step::before {
            content: '';
            position: absolute;
            left: -41px;
            top: 20px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #667eea;
            border: 3px solid white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .template-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .template-item {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .template-item:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>FSU Content Calendar</h1>
            <p>Restructured for clarity, efficiency, and impact</p>
        </div>
        
        <div class="tabs">
            <button class="tab active" onclick="switchTab('weekly')">Weekly View</button>
            <button class="tab" onclick="switchTab('pillars')">Content Pillars</button>
            <button class="tab" onclick="switchTab('platforms')">Platform Strategy</button>
            <button class="tab" onclick="switchTab('workflow')">Production Workflow</button>
            <button class="tab" onclick="switchTab('analytics')">Analytics Dashboard</button>
            <button class="tab" onclick="switchTab('templates')">Templates & Assets</button>
        </div>
        
        <div class="content">
            <!-- Weekly View -->
            <div id="weekly" class="tab-content active">
                <h2>Weekly Content Schedule</h2>
                <p style="margin: 10px 0; color: #666;">Week of 11 September 2025 – Drag to reschedule, click to edit</p>
                
                <div class="calendar-grid">
                    <div class="day-header">Monday</div>
                    <div class="day-header">Tuesday</div>
                    <div class="day-header">Wednesday</div>
                    <div class="day-header">Thursday</div>
                    <div class="day-header">Friday</div>
                    <div class="day-header">Saturday</div>
                    <div class="day-header">Sunday</div>
                    
                    <div class="day-cell">
                        <div class="content-item">
                            <span class="status-indicator status-ready"></span>
                            <span class="platform-badge badge-email">Email</span>
                            Weekly Blast: Legal Updates
                        </div>
                        <div class="content-item">
                            <span class="status-indicator status-draft"></span>
                            <span class="platform-badge badge-fb">FB</span>
                            Morning Post: Case Win
                        </div>
                    </div>
                    
                    <div class="day-cell">
                        <div class="content-item">
                            <span class="status-indicator status-scheduled"></span>
                            <span class="platform-badge badge-x">X</span>
                            Thread: University Policy
                        </div>
                        <div class="content-item">
                            <span class="status-indicator status-draft"></span>
                            <span class="platform-badge badge-ig">IG</span>
                            Quote Graphic
                        </div>
                    </div>
                    
                    <div class="day-cell">
                        <div class="content-item">
                            <span class="status-indicator status-ready"></span>
                            <span class="platform-badge badge-podcast">Podcast</span>
                            "In That Case" Episode
                        </div>
                    </div>
                    
                    <div class="day-cell">
                        <div class="content-item">
                            <span class="status-indicator status-draft"></span>
                            <span class="platform-badge badge-tt">TikTok</span>
                            Explainer: Free Speech 101
                        </div>
                        <div class="content-item">
                            <span class="status-indicator status-scheduled"></span>
                            <span class="platform-badge badge-li">LinkedIn</span>
                            Article: Workplace Rights
                        </div>
                    </div>
                    
                    <div class="day-cell">
                        <div class="content-item">
                            <span class="status-indicator status-ready"></span>
                            <span class="platform-badge badge-yt">YouTube</span>
                            Long-form Interview
                        </div>
                        <div class="content-item">
                            <span class="status-indicator status-draft"></span>
                            <span class="platform-badge badge-fb">FB</span>
                            Poll of the Week
                        </div>
                    </div>
                    
                    <div class="day-cell">
                        <div class="content-item">
                            <span class="status-indicator status-scheduled"></span>
                            <span class="platform-badge badge-ig">IG</span>
                            Weekend Roundup
                        </div>
                    </div>
                    
                    <div class="day-cell">
                        <div class="content-item">
                            <span class="status-indicator status-draft"></span>
                            <span class="platform-badge badge-x">X</span>
                            Week Ahead Preview
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Content Pillars -->
            <div id="pillars" class="tab-content">
                <h2>Content Pillars Strategy</h2>
                
                <div class="pillar-section">
                    <div class="pillar-header">Legal & Rights Updates (30%)</div>
                    <div class="pillar-content">
                        <div class="pillar-card">
                            <h4>Case Updates</h4>
                            <p>Active cases, wins, progress reports</p>
                            <small>Formats: Email blasts, social posts, media releases</small>
                        </div>
                        <div class="pillar-card">
                            <h4>Policy Analysis</h4>
                            <p>Government bills, university policies, workplace rules</p>
                            <small>Formats: Op-eds, LinkedIn articles, YouTube explainers</small>
                        </div>
                        <div class="pillar-card">
                            <h4>Legal Resources</h4>
                            <p>Know your rights guides, templates, FAQs</p>
                            <small>Formats: Downloadables, Instagram carousels</small>
                        </div>
                    </div>
                </div>
                
                <div class="pillar-section">
                    <div class="pillar-header">Community Stories (25%)</div>
                    <div class="pillar-content">
                        <div class="pillar-card">
                            <h4>Member Spotlights</h4>
                            <p>Success stories, testimonials, case studies</p>
                            <small>Formats: Video interviews, quote graphics</small>
                        </div>
                        <div class="pillar-card">
                            <h4>Event Coverage</h4>
                            <p>Rallies, meetings, community gatherings</p>
                            <small>Formats: Live social coverage, photo galleries</small>
                        </div>
                        <div class="pillar-card">
                            <h4>Partner Features</h4>
                            <p>Allied organisations, collaborative efforts</p>
                            <small>Formats: Cross-posts, podcast interviews</small>
                        </div>
                    </div>
                </div>
                
                <div class="pillar-section">
                    <div class="pillar-header">Educational Content (25%)</div>
                    <div class="pillar-content">
                        <div class="pillar-card">
                            <h4>Free Speech 101</h4>
                            <p>Basic concepts, historical context, global perspectives</p>
                            <small>Formats: TikTok explainers, infographics</small>
                        </div>
                        <div class="pillar-card">
                            <h4>Debate & Discussion</h4>
                            <p>Complex issues, nuanced takes, expert panels</p>
                            <small>Formats: Podcast deep-dives, YouTube panels</small>
                        </div>
                        <div class="pillar-card">
                            <h4>Practical Guides</h4>
                            <p>How to file complaints, write submissions, organise</p>
                            <small>Formats: Blog posts, downloadable PDFs</small>
                        </div>
                    </div>
                </div>
                
                <div class="pillar-section">
                    <div class="pillar-header">Cultural Commentary (20%)</div>
                    <div class="pillar-content">
                        <div class="pillar-card">
                            <h4>Current Events</h4>
                            <p>Breaking news responses, trend analysis</p>
                            <small>Formats: X threads, Instagram stories</small>
                        </div>
                        <div class="pillar-card">
                            <h4>Opinion Pieces</h4>
                            <p>Board member perspectives, guest columns</p>
                            <small>Formats: Op-eds, LinkedIn articles</small>
                        </div>
                        <div class="pillar-card">
                            <h4>Weekly Polls</h4>
                            <p>Community sentiment, topical questions</p>
                            <small>Formats: All platform polls, result graphics</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Platform Strategy -->
            <div id="platforms" class="tab-content">
                <h2>Platform-Specific Strategy</h2>
                
                <div class="pillar-section">
                    <div class="pillar-header">📧 Email (Blasts)</div>
                    <div class="pillar-content">
                        <div class="pillar-card">
                            <h4>Frequency</h4>
                            <p>Weekly newsletter + urgent updates</p>
                            <small>Tuesdays 10am NZST</small>
                        </div>
                        <div class="pillar-card">
                            <h4>Content Mix</h4>
                            <p>Legal updates, member stories, events</p>
                            <small>3-5 sections per blast</small>
                        </div>
                        <div class="pillar-card">
                            <h4>KPIs</h4>
                            <p>Open rate: 35%+, Click rate: 8%+</p>
                            <small>Track: Conversions to donations</small>
                        </div>
                    </div>
                </div>
                
                <div class="pillar-section">
                    <div class="pillar-header">📘 Facebook</div>
                    <div class="pillar-content">
                        <div class="pillar-card">
                            <h4>Frequency</h4>
                            <p>1-2 posts daily</p>
                            <small>Peak: 8am, 12pm, 6pm</small>
                        </div>
                        <div class="pillar-card">
                            <h4>Best Content</h4>
                            <p>Local stories, event photos, polls</p>
                            <small>Focus: Community engagement</small>
                        </div>
                        <div class="pillar-card">
                            <h4>KPIs</h4>
                            <p>Engagement rate: 4%+</p>
                            <small>Comments quality over quantity</small>
                        </div>
                    </div>
                </div>
                
                <div class="pillar-section">
                    <div class="pillar-header">📸 Instagram</div>
                    <div class="pillar-content">
                        <div class="pillar-card">
                            <h4>Frequency</h4>
                            <p>4-5x weekly + daily stories</p>
                            <small>Peak: 7am, 5pm</small>
                        </div>
                        <div class="pillar-card">
                            <h4>Best Content</h4>
                            <p>Quote graphics, carousels, reels</p>
                            <small>Visual storytelling focus</small>
                        </div>
                        <div class="pillar-card">
                            <h4>KPIs</h4>
                            <p>Story views, saves, shares</p>
                            <small>Reach growth: 10% monthly</small>
                        </div>
                    </div>
                </div>
                
                <div class="pillar-section">
                    <div class="pillar-header">✖️ X (Twitter)</div>
                    <div class="pillar-content">
                        <div class="pillar-card">
                            <h4>Frequency</h4>
                            <p>3-5 posts daily</p>
                            <small>Real-time responses key</small>
                        </div>
                        <div class="pillar-card">
                            <h4>Best Content</h4>
                            <p>Breaking news, threads, debates</p>
                            <small>Quick takes, sharp commentary</small>
                        </div>
                        <div class="pillar-card">
                            <h4>KPIs</h4>
                            <p>Impressions, retweets, replies</p>
                            <small>Influence score tracking</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Production Workflow -->
            <div id="workflow" class="tab-content">
                <h2>Content Production Workflow</h2>
                
                <div class="workflow-timeline">
                    <div class="workflow-step">
                        <h4>Monday: Planning</h4>
                        <p>Weekly content meeting, review analytics, assign tasks</p>
                        <small>Tools: Google Sheets, Slack</small>
                    </div>
                    
                    <div class="workflow-step">
                        <h4>Tuesday-Wednesday: Creation</h4>
                        <p>Draft copy, design graphics, record videos/podcasts</p>
                        <small>Tools: Canva, Descript, Google Docs</small>
                    </div>
                    
                    <div class="workflow-step">
                        <h4>Thursday: Review & Approval</h4>
                        <p>Legal check, fact-check, stakeholder sign-off</p>
                        <small>Process: Draft → Review → Revise → Approve</small>
                    </div>
                    
                    <div class="workflow-step">
                        <h4>Friday: Scheduling</h4>
                        <p>Bulk upload to Hootsuite, set publishing times</p>
                        <small>Tools: Hootsuite, Later, Buzzsprout</small>
                    </div>
                    
                    <div class="workflow-step">
                        <h4>Daily: Engagement</h4>
                        <p>Monitor comments, respond to messages, track mentions</p>
                        <small>Response time target: < 2 hours</small>
                    </div>
                </div>
                
                <div class="pillar-section">
                    <div class="pillar-header">Approval Matrix</div>
                    <div class="pillar-content">
                        <div class="pillar-card">
                            <h4>Green Light (No approval)</h4>
                            <p>Scheduled posts, evergreen content, event promotion</p>
                        </div>
                        <div class="pillar-card">
                            <h4>Yellow Light (Manager approval)</h4>
                            <p>Media responses, opinion pieces, partner content</p>
                        </div>
                        <div class="pillar-card">
                            <h4>Red Light (Board approval)</h4>
                            <p>Legal statements, crisis responses, policy positions</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Analytics Dashboard -->
            <div id="analytics" class="tab-content">
                <h2>Analytics Dashboard</h2>
                
                <div class="metric-grid">
                    <div class="metric-card">
                        <div>Total Reach</div>
                        <div class="metric-value">247K</div>
                        <div>↑ 15% this month</div>
                    </div>
                    
                    <div class="metric-card">
                        <div>Engagement Rate</div>
                        <div class="metric-value">4.8%</div>
                        <div>↑ 0.3% from last month</div>
                    </div>
                    
                    <div class="metric-card">
                        <div>Email Subscribers</div>
                        <div class="metric-value">8,432</div>
                        <div>↑ 234 new this month</div>
                    </div>
                    
                    <div class="metric-card">
                        <div>Media Mentions</div>
                        <div class="metric-value">42</div>
                        <div>78% positive sentiment</div>
                    </div>
                </div>
                
                <div class="pillar-section">
                    <div class="pillar-header">Content Performance by Type</div>
                    <div class="pillar-content">
                        <div class="pillar-card">
                            <h4>🏆 Top Performer</h4>
                            <p>Video explainers on TikTok</p>
                            <small>Avg. 15K views, 12% engagement</small>
                        </div>
                        <div class="pillar-card">
                            <h4>📈 Growing Fast</h4>
                            <p>LinkedIn long-form articles</p>
                            <small>300% increase in impressions</small>
                        </div>
                        <div class="pillar-card">
                            <h4>⚠️ Needs Attention</h4>
                            <p>YouTube subscriber growth</p>
                            <small>Only 2% monthly growth</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Templates & Assets -->
            <div id="templates" class="tab-content">
                <h2>Templates & Asset Library</h2>
                
                <div class="template-gallery">
                    <div class="template-item">
                        <h4>📧 Email Blast</h4>
                        <p>Standard newsletter template</p>
                    </div>
                    <div class="template-item">
                        <h4>📢 Media Release</h4>
                        <p>Press release format</p>
                    </div>
                    <div class="template-item">
                        <h4>💬 Quote Graphic</h4>
                        <p>Instagram/FB template</p>
                    </div>
                    <div class="template-item">
                        <h4>🎥 Video Intro</h4>
                        <p>Standard opening/closing</p>
                    </div>
                    <div class="template-item">
                        <h4>📊 Poll Results</h4>
                        <p>Data visualisation template</p>
                    </div>
                    <div class="template-item">
                        <h4>🎙️ Podcast Script</h4>
                        <p>Episode structure guide</p>
                    </div>
                    <div class="template-item">
                        <h4>📝 Op-ed Structure</h4>
                        <p>750-word article template</p>
                    </div>
                    <div class="template-item">
                        <h4>🚨 Crisis Response</h4>
                        <p>Rapid response framework</p>
                    </div>
                </div>
                
                <div class="pillar-section">
                    <div class="pillar-header">Brand Guidelines Quick Reference</div>
                    <div class="pillar-content">
                        <div class="pillar-card">
                            <h4>Voice & Tone</h4>
                            <p>Authoritative but approachable, factual, respectful debate</p>
                        </div>
                        <div class="pillar-card">
                            <h4>Visual Identity</h4>
                            <p>Primary colours, logo usage, typography standards</p>
                        </div>
                        <div class="pillar-card">
                            <h4>Messaging Framework</h4>
                            <p>Key messages, proof points, call-to-actions</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function switchTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });
            
            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab content
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked tab
            event.target.classList.add('active');
        }
    </script>
</body>
</html>