<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FSU Content Strategy - Project Tracker</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f0f4f8;
            padding: 20px;
            color: #2d3748;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
        }
        
        .header h1 {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
        }
        
        .progress-overview {
            padding: 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .progress-bar {
            background: #e0e0e0;
            height: 30px;
            border-radius: 15px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            background: linear-gradient(90deg, #10b981 0%, #059669 100%);
            height: 100%;
            width: 15%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            transition: width 0.5s ease;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        
        .stat-value {
            font-size: 1.8em;
            font-weight: bold;
            color: #667eea;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
        
        .phases {
            padding: 30px;
        }
        
        .phase {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            overflow: hidden;
        }
        
        .phase-header {
            background: #f8f9fa;
            padding: 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background 0.3s;
        }
        
        .phase-header:hover {
            background: #f0f0f0;
        }
        
        .phase-title {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .phase-number {
            background: #667eea;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .phase-name {
            font-size: 1.2em;
            font-weight: bold;
        }
        
        .phase-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .status-badge {
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: bold;
        }
        
        .status-not-started { background: #f3f4f6; color: #6b7280; }
        .status-in-progress { background: #fef3c7; color: #92400e; }
        .status-completed { background: #d1fae5; color: #065f46; }
        .status-blocked { background: #fee2e2; color: #991b1b; }
        
        .phase-content {
            padding: 20px;
            display: none;
        }
        
        .phase-content.active {
            display: block;
        }
        
        .task-list {
            list-style: none;
            margin-top: 15px;
        }
        
        .task-item {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            padding: 12px;
            background: #f9f9f9;
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s;
        }
        
        .task-item:hover {
            background: #f0f0f0;
            transform: translateX(5px);
        }
        
        .task-checkbox {
            width: 20px;
            height: 20px;
            cursor: pointer;
        }
        
        .task-details {
            flex: 1;
        }
        
        .task-name {
            font-weight: 500;
            margin-bottom: 5px;
        }
        
        .task-description {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        .task-meta {
            display: flex;
            gap: 15px;
            font-size: 0.85em;
            color: #999;
        }
        
        .priority-high { color: #dc2626; font-weight: bold; }
        .priority-medium { color: #f59e0b; }
        .priority-low { color: #10b981; }
        
        .milestone {
            background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .milestone::before {
            content: '🎯';
            font-size: 1.5em;
        }
        
        .timeline {
            padding: 30px;
            background: #f8f9fa;
        }
        
        .timeline-header {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 20px;
        }
        
        .timeline-grid {
            display: grid;
            grid-template-columns: 100px 1fr;
            gap: 20px;
            align-items: start;
        }
        
        .timeline-date {
            font-weight: bold;
            color: #667eea;
            text-align: right;
        }
        
        .timeline-content {
            border-left: 3px solid #667eea;
            padding-left: 20px;
            position: relative;
        }
        
        .timeline-content::before {
            content: '';
            position: absolute;
            left: -8px;
            top: 5px;
            width: 13px;
            height: 13px;
            border-radius: 50%;
            background: #667eea;
            border: 3px solid white;
        }
        
        .resources {
            padding: 30px;
        }
        
        .resource-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .resource-card {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s;
            cursor: pointer;
        }
        
        .resource-card:hover {
            border-color: #667eea;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .resource-icon {
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .resource-title {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .resource-description {
            font-size: 0.9em;
            color: #666;
        }
        
        .notes-section {
            padding: 30px;
            background: #fffbeb;
            border-top: 3px solid #fbbf24;
        }
        
        .notes-box {
            background: white;
            border: 1px solid #fde68a;
            border-radius: 8px;
            padding: 20px;
            margin-top: 15px;
        }
        
        .notes-box h4 {
            color: #92400e;
            margin-bottom: 10px;
        }
        
        .notes-box ul {
            margin-left: 20px;
            color: #78716c;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>FSU Content Strategy Overhaul</h1>
            <p>Project Tracker – From chaos to clarity in 6 weeks</p>
        </div>
        
        <div class="progress-overview">
            <h2>Overall Progress</h2>
            <div class="progress-bar">
                <div class="progress-fill">15%</div>
            </div>
            
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">3</div>
                    <div class="stat-label">Tasks Complete</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">17</div>
                    <div class="stat-label">Tasks Remaining</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">Week 1</div>
                    <div class="stat-label">Current Phase</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">6 weeks</div>
                    <div class="stat-label">Time to Launch</div>
                </div>
            </div>
        </div>
        
        <div class="phases">
            <div class="phase">
                <div class="phase-header" onclick="togglePhase(1)">
                    <div class="phase-title">
                        <div class="phase-number">1</div>
                        <div>
                            <div class="phase-name">Audit & Setup</div>
                            <div style="font-size: 0.9em; color: #666;">Week 1-2: Foundation work</div>
                        </div>
                    </div>
                    <div class="phase-status">
                        <span class="status-badge status-in-progress">In Progress</span>
                        <span>▼</span>
                    </div>
                </div>
                
                <div class="phase-content active" id="phase-1">
                    <ul class="task-list">
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox" checked>
                            <div class="task-details">
                                <div class="task-name">✅ Analyse current content planner</div>
                                <div class="task-description">Review existing spreadsheet structure and identify gaps</div>
                                <div class="task-meta">
                                    <span class="priority-high">Priority: High</span>
                                    <span>Owner: You</span>
                                    <span>✓ Completed</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox" checked>
                            <div class="task-details">
                                <div class="task-name">✅ Create new calendar structure</div>
                                <div class="task-description">Design weekly view, content pillars, platform strategies</div>
                                <div class="task-meta">
                                    <span class="priority-high">Priority: High</span>
                                    <span>Owner: You</span>
                                    <span>✓ Completed</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Clean up existing spreadsheet</div>
                                <div class="task-description">Fix typos, merge duplicates, archive old content</div>
                                <div class="task-meta">
                                    <span class="priority-high">Priority: High</span>
                                    <span>Owner: You</span>
                                    <span>Due: Tomorrow</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Audit all platform accounts</div>
                                <div class="task-description">Check access, bio updates, link consistency</div>
                                <div class="task-meta">
                                    <span class="priority-medium">Priority: Medium</span>
                                    <span>Owner: You</span>
                                    <span>Due: Friday</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Set up Hootsuite properly</div>
                                <div class="task-description">Connect all accounts, create posting schedules, set up streams</div>
                                <div class="task-meta">
                                    <span class="priority-medium">Priority: Medium</span>
                                    <span>Owner: You</span>
                                    <span>Due: Friday</span>
                                </div>
                            </div>
                        </li>
                    </ul>
                    
                    <div class="milestone">
                        Milestone: All systems audited and basic structure in place
                    </div>
                </div>
            </div>
            
            <div class="phase">
                <div class="phase-header" onclick="togglePhase(2)">
                    <div class="phase-title">
                        <div class="phase-number">2</div>
                        <div>
                            <div class="phase-name">Content Strategy Development</div>
                            <div style="font-size: 0.9em; color: #666;">Week 2-3: Define pillars and workflows</div>
                        </div>
                    </div>
                    <div class="phase-status">
                        <span class="status-badge status-not-started">Not Started</span>
                        <span>▼</span>
                    </div>
                </div>
                
                <div class="phase-content" id="phase-2">
                    <ul class="task-list">
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Define content pillars properly</div>
                                <div class="task-description">Lock in 4 main pillars with percentage splits</div>
                                <div class="task-meta">
                                    <span class="priority-high">Priority: High</span>
                                    <span>Owner: You + Board</span>
                                    <span>Due: Week 2</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Create content templates</div>
                                <div class="task-description">Build Canva templates for each content type</div>
                                <div class="task-meta">
                                    <span class="priority-medium">Priority: Medium</span>
                                    <span>Owner: You</span>
                                    <span>Due: Week 3</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Establish approval workflow</div>
                                <div class="task-description">Document who approves what, response times</div>
                                <div class="task-meta">
                                    <span class="priority-high">Priority: High</span>
                                    <span>Owner: You + Management</span>
                                    <span>Due: Week 2</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Develop voice & tone guide</div>
                                <div class="task-description">Document FSU's communication style</div>
                                <div class="task-meta">
                                    <span class="priority-medium">Priority: Medium</span>
                                    <span>Owner: You</span>
                                    <span>Due: Week 3</span>
                                </div>
                            </div>
                        </li>
                    </ul>
                    
                    <div class="milestone">
                        Milestone: Complete content strategy documented and approved
                    </div>
                </div>
            </div>
            
            <div class="phase">
                <div class="phase-header" onclick="togglePhase(3)">
                    <div class="phase-title">
                        <div class="phase-number">3</div>
                        <div>
                            <div class="phase-name">Analytics & Measurement</div>
                            <div style="font-size: 0.9em; color: #666;">Week 3-4: Set up tracking systems</div>
                        </div>
                    </div>
                    <div class="phase-status">
                        <span class="status-badge status-not-started">Not Started</span>
                        <span>▼</span>
                    </div>
                </div>
                
                <div class="phase-content" id="phase-3">
                    <ul class="task-list">
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Set up analytics dashboard</div>
                                <div class="task-description">Google Analytics, social insights, email metrics</div>
                                <div class="task-meta">
                                    <span class="priority-high">Priority: High</span>
                                    <span>Owner: You</span>
                                    <span>Due: Week 3</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Create reporting template</div>
                                <div class="task-description">Monthly report format for board/stakeholders</div>
                                <div class="task-meta">
                                    <span class="priority-medium">Priority: Medium</span>
                                    <span>Owner: You</span>
                                    <span>Due: Week 4</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Implement sentiment tracking</div>
                                <div class="task-description">System for tracking media coverage tone</div>
                                <div class="task-meta">
                                    <span class="priority-low">Priority: Low</span>
                                    <span>Owner: You</span>
                                    <span>Due: Week 4</span>
                                </div>
                            </div>
                        </li>
                    </ul>
                    
                    <div class="milestone">
                        Milestone: Full analytics system operational
                    </div>
                </div>
            </div>
            
            <div class="phase">
                <div class="phase-header" onclick="togglePhase(4)">
                    <div class="phase-title">
                        <div class="phase-number">4</div>
                        <div>
                            <div class="phase-name">Team Training & Handover</div>
                            <div style="font-size: 0.9em; color: #666;">Week 5: Upskill the team</div>
                        </div>
                    </div>
                    <div class="phase-status">
                        <span class="status-badge status-not-started">Not Started</span>
                        <span>▼</span>
                    </div>
                </div>
                
                <div class="phase-content" id="phase-4">
                    <ul class="task-list">
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Create process documentation</div>
                                <div class="task-description">Step-by-step guides for all workflows</div>
                                <div class="task-meta">
                                    <span class="priority-high">Priority: High</span>
                                    <span>Owner: You</span>
                                    <span>Due: Week 5</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Run team training sessions</div>
                                <div class="task-description">Hootsuite, Canva, new calendar system</div>
                                <div class="task-meta">
                                    <span class="priority-high">Priority: High</span>
                                    <span>Owner: You</span>
                                    <span>Due: Week 5</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Set up backup systems</div>
                                <div class="task-description">Ensure continuity when you hand over</div>
                                <div class="task-meta">
                                    <span class="priority-medium">Priority: Medium</span>
                                    <span>Owner: You</span>
                                    <span>Due: Week 5</span>
                                </div>
                            </div>
                        </li>
                    </ul>
                    
                    <div class="milestone">
                        Milestone: Team fully trained and self-sufficient
                    </div>
                </div>
            </div>
            
            <div class="phase">
                <div class="phase-header" onclick="togglePhase(5)">
                    <div class="phase-title">
                        <div class="phase-number">5</div>
                        <div>
                            <div class="phase-name">Launch & Monitor</div>
                            <div style="font-size: 0.9em; color: #666;">Week 6: Go live and refine</div>
                        </div>
                    </div>
                    <div class="phase-status">
                        <span class="status-badge status-not-started">Not Started</span>
                        <span>▼</span>
                    </div>
                </div>
                
                <div class="phase-content" id="phase-5">
                    <ul class="task-list">
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Launch new content calendar</div>
                                <div class="task-description">Start posting according to new structure</div>
                                <div class="task-meta">
                                    <span class="priority-high">Priority: High</span>
                                    <span>Owner: Team</span>
                                    <span>Due: Week 6</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">Daily monitoring & adjustments</div>
                                <div class="task-description">Track performance, fix issues quickly</div>
                                <div class="task-meta">
                                    <span class="priority-high">Priority: High</span>
                                    <span>Owner: You</span>
                                    <span>Due: Week 6</span>
                                </div>
                            </div>
                        </li>
                        
                        <li class="task-item">
                            <input type="checkbox" class="task-checkbox">
                            <div class="task-details">
                                <div class="task-name">First week review</div>
                                <div class="task-description">Assess what's working, what needs tweaking</div>
                                <div class="task-meta">
                                    <span class="priority-medium">Priority: Medium</span>
                                    <span>Owner: You + Team</span>
                                    <span>Due: End Week 6</span>
                                </div>
                            </div>
                        </li>
                    </ul>
                    
                    <div class="milestone">
                        Milestone: New system fully operational
                    </div>
                </div>
            </div>
        </div>
        
        <div class="timeline">
            <h2 class="timeline-header">Key Dates & Deadlines</h2>
            
            <div class="timeline-grid">
                <div class="timeline-date">Today</div>
                <div class="timeline-content">
                    <strong>Project kickoff</strong><br>
                    Begin spreadsheet cleanup and account audit
                </div>
                
                <div class="timeline-date">Friday</div>
                <div class="timeline-content">
                    <strong>Week 1 Complete</strong><br>
                    All accounts audited, Hootsuite configured
                </div>
                
                <div class="timeline-date">18 Sep</div>
                <div class="timeline-content">
                    <strong>Strategy lock-in</strong><br>
                    Content pillars and approval workflow finalised
                </div>
                
                <div class="timeline-date">25 Sep</div>
                <div class="timeline-content">
                    <strong>Analytics ready</strong><br>
                    Dashboard and reporting systems operational
                </div>
                
                <div class="timeline-date">2 Oct</div>
                <div class="timeline-content">
                    <strong>Team training</strong><br>
                    All team members trained on new systems
                </div>
                
                <div class="timeline-date">9 Oct</div>
                <div class="timeline-content">
                    <strong>Full launch</strong><br>
                    New content strategy goes live
                </div>
                
                <div class="timeline-date">16 Oct</div>
                <div class="timeline-content">
                    <strong>First review</strong><br>
                    Assess performance and make adjustments
                </div>
            </div>
        </div>
        
        <div class="resources">
            <h2>Quick Resources</h2>
            
            <div class="resource-grid">
                <div class="resource-card">
                    <div class="resource-icon">📊</div>
                    <div class="resource-title">Current Spreadsheet</div>
                    <div class="resource-description">Link to existing content planner</div>
                </div>
                
                <div class="resource-card">
                    <div class="resource-icon">🎨</div>
                    <div class="resource-title">Canva Team</div>
                    <div class="resource-description">Access templates and brand kit</div>
                </div>
                
                <div class="resource-card">
                    <div class="resource-icon">📅</div>
                    <div class="resource-title">Hootsuite</div>
                    <div class="resource-description">Social scheduling platform</div>
                </div>
                
                <div class="resource-card">
                    <div class="resource-icon">🎙️</div>
                    <div class="resource-title">Buzzsprout</div>
                    <div class="resource-description">Podcast hosting & distribution</div>
                </div>
                
                <div class="resource-card">
                    <div class="resource-icon">✂️</div>
                    <div class="resource-title">Descript</div>
                    <div class="resource-description">Video/audio editing tool</div>
                </div>
                
                <div class="resource-card">
                    <div class="resource-icon">📈</div>
                    <div class="resource-title">Analytics Guide</div>
                    <div class="resource-description">How to track what matters</div>
                </div>
            </div>
        </div>
        
        <div class="notes-section">
            <h2>🔔 Important Notes</h2>
            
            <div class="notes-box">
                <h4>Quick Wins This Week:</h4>
                <ul>
                    <li>Fix those spreadsheet typos – looks unprofessional</li>
                    <li>Update all social media bios with consistent messaging</li>
                    <li>Create 5 evergreen posts for emergency backup</li>
                    <li>Set up basic Hootsuite scheduling for next week</li>
                </ul>
            </div>
            
            <div class="notes-box">
                <h4>Watch Out For:</h4>
                <ul>
                    <li>Don't over-complicate – start simple and build</li>
                    <li>Get buy-in from key stakeholders early</li>
                    <li>Document everything as you go</li>
                    <li>Keep old system running parallel until confident</li>
                </ul>
            </div>
            
            <div class="notes-box">
                <h4>Success Metrics:</h4>
                <ul>
                    <li>50% reduction in content production time</li>
                    <li>Consistent posting schedule across all platforms</li>
                    <li>20% increase in engagement within first month</li>
                    <li>Clear analytics reporting for board meetings</li>
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        function togglePhase(phaseNumber) {
            const phaseContent = document.getElementById(`phase-${phaseNumber}`);
            phaseContent.classList.toggle('active');
        }
        
        // Update progress based on checked boxes
        function updateProgress() {
            const checkboxes = document.querySelectorAll('.task-checkbox');
            const checked = document.querySelectorAll('.task-checkbox:checked');
            const percentage = Math.round((checked.length / checkboxes.length) * 100);
            
            document.querySelector('.progress-fill').style.width = percentage + '%';
            document.querySelector('.progress-fill').textContent = percentage + '%';
            
            // Update stats
            document.querySelectorAll('.stat-value')[0].textContent = checked.length;
            document.querySelectorAll('.stat-value')[1].textContent = checkboxes.length - checked.length;
        }
        
        // Add event listeners to checkboxes
        document.querySelectorAll('.task-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', updateProgress);
        });
        
        // Initial progress update
        updateProgress();
    </script>
</body>
</html>